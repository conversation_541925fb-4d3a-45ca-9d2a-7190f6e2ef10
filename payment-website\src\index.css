/* PayShop - Complete CSS Styles */
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-blue: #667eea;
  --primary-purple: #764ba2;
  --azure-blue: #0ea5e9;
  --light-blue: #3b82f6;
  --dark-blue: #1d4ed8;
  --text-dark: #1f2937;
  --text-gray: #666;
  --text-light: #999;
  --background-light: #f8fafc;
  --background-gray: #f1f5f9;
  --white: #ffffff;
  --red: #dc3545;
  --green: #28a745;
  --yellow: #ffc107;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-azure: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  --gradient-background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

  /* Shadows */
  --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-azure: 0 4px 14px 0 rgba(56, 189, 248, 0.39);
  --shadow-glow: 0 0 20px rgba(56, 189, 248, 0.5);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border radius */
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===== GLOBAL RESET ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  max-width: 100%;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background: var(--gradient-background);
  overflow-x: hidden;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: 100px; /* Space for floating navbar */
}

/* ===== BASIC ELEMENTS ===== */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all var(--transition-normal);
}

a {
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-normal);
}

input, textarea, select {
  font-family: inherit;
  outline: none;
  transition: all var(--transition-normal);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-azure);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

/* ===== BUTTON STYLES ===== */
.btn-primary {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: var(--white);
  font-weight: 600;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-large);
  transition: all 0.3s ease-out;
  transform: scale(1);
}

.btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #1e40af);
  box-shadow: var(--shadow-azure);
  transform: scale(1.05);
}

.btn-secondary {
  background: linear-gradient(to right, #f3f4f6, #e5e7eb);
  color: var(--text-dark);
  font-weight: 600;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease-out;
  transform: scale(1);
}

.btn-secondary:hover {
  background: linear-gradient(to right, #e5e7eb, #d1d5db);
  box-shadow: var(--shadow-large);
  transform: scale(1.05);
}

.btn-azure {
  background: linear-gradient(to right, var(--light-blue), var(--azure-blue));
  color: var(--white);
  font-weight: 600;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-large);
  transition: all 0.3s ease-out;
  transform: scale(1);
  animation: azure-pulse 3s ease-in-out infinite;
}

.btn-azure:hover {
  background: linear-gradient(to right, var(--azure-blue), var(--dark-blue));
  box-shadow: var(--shadow-azure);
  transform: scale(1.05);
}

/* ===== CARD STYLES ===== */
.card-premium {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-large);
  border: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.3s ease-out;
  transform: scale(1);
}

.card-premium:hover {
  box-shadow: var(--shadow-azure);
  transform: scale(1.05);
}

.card-product {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-large);
  border: 1px solid #f3f4f6;
  overflow: hidden;
  transition: all 0.5s ease-out;
  transform: scale(1) translateY(0);
}

.card-product:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transform: scale(1.05) translateY(-8px);
}

/* ===== INPUT STYLES ===== */
.input-floating {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid #d1d5db;
  border-radius: var(--radius-xl);
  transition: all 0.3s;
}

.input-floating:focus {
  border-color: var(--light-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-floating::placeholder {
  color: transparent;
}

.label-floating {
  position: absolute;
  left: var(--spacing-lg);
  top: var(--spacing-lg);
  color: var(--text-gray);
  transition: all 0.3s;
  pointer-events: none;
}

/* ===== UTILITY CLASSES ===== */
.gradient-text {
  background: linear-gradient(to right, var(--light-blue), var(--primary-purple), var(--dark-blue));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.hero-gradient {
  background: linear-gradient(to bottom right, var(--light-blue), var(--primary-purple), var(--dark-blue));
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s linear infinite;
}

/* ===== LAYOUT CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.min-h-screen {
  min-height: 100vh;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.sticky {
  position: sticky;
}

.top-0 {
  top: 0;
}

.z-50 {
  z-index: 50;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* ===== TEXT UTILITIES ===== */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.text-center {
  text-align: center;
}

.text-white {
  color: var(--white);
}

.text-gray-700 {
  color: #374151;
}

.text-blue-600 {
  color: var(--light-blue);
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* ===== SPACING UTILITIES ===== */
.p-4 {
  padding: var(--spacing-md);
}

.px-4 {
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

.py-3 {
  padding-top: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
}

.mb-4 {
  margin-bottom: var(--spacing-md);
}

.mb-6 {
  margin-bottom: var(--spacing-xl);
}

.space-x-2 > * + * {
  margin-left: var(--spacing-sm);
}

.space-x-8 > * + * {
  margin-left: var(--spacing-xl);
}

/* ===== BACKGROUND UTILITIES ===== */
.bg-white {
  background-color: var(--white);
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

/* ===== BORDER UTILITIES ===== */
.border {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.rounded-lg {
  border-radius: var(--radius-md);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

/* ===== ANIMATION UTILITIES ===== */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* ===== KEYFRAME ANIMATIONS ===== */
@keyframes azure-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(56, 189, 248, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(56, 189, 248, 0.8), 0 0 30px rgba(56, 189, 248, 0.6);
    transform: scale(1.02);
  }
}

@keyframes azure-blink {
  0%, 100% {
    box-shadow: 0 0 5px rgba(135, 206, 250, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(135, 206, 250, 0.8), 0 0 30px rgba(135, 206, 250, 0.6);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-azure-glow {
  animation: azure-pulse 3s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* ===== NAVBAR STYLES ===== */
.navbar {
  position: fixed;
  top: 20px;
  left: 0;
  right: 0;
  z-index: 50;
  background: transparent;
  pointer-events: none; /* Allow clicks to pass through transparent areas */
}

.navbar-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.navbar-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  pointer-events: auto; /* Re-enable clicks for the actual content */
}

.navbar-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
}

.navbar-logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(to bottom right, var(--light-blue), var(--primary-purple));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 700;
  font-size: 1.125rem;
  animation: azure-pulse 3s ease-in-out infinite;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--light-blue), var(--primary-purple), var(--dark-blue));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.navbar-link {
  position: relative;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all var(--transition-normal);
}

.navbar-link.active {
  color: var(--light-blue);
  background-color: #eff6ff;
}

.navbar-link:not(.active) {
  color: #374151;
}

.navbar-link:hover {
  color: var(--light-blue);
  background-color: #f9fafb;
}

.navbar-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--light-blue), var(--primary-purple));
  border-radius: 1px;
}

.cart-badge {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 1.25rem;
  height: 1.25rem;
  background: linear-gradient(to right, #ef4444, #ec4899);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 0.75rem;
  font-weight: 700;
}

/* Mobile header styles */
.mobile-header {
  display: none;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.navbar-logo-mobile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
}

.navbar-logo-mobile:hover {
  transform: scale(1.05);
}

.mobile-menu-button {
  display: none;
  background: none;
  color: #374151;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.mobile-menu-button:hover {
  background-color: #f9fafb;
}

.mobile-menu {
  padding: var(--spacing-md) 0;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  animation: fade-in-down 0.3s ease-out;
}

.mobile-menu-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.mobile-menu-link {
  padding: var(--spacing-lg) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-menu-link.active {
  color: var(--light-blue);
  background-color: #eff6ff;
}

.mobile-menu-link:not(.active) {
  color: #374151;
}

.mobile-menu-link:hover {
  color: var(--light-blue);
  background-color: #f9fafb;
}

/* ===== PILL NAVIGATION STYLES ===== */
.navbar-menu-pill {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 50px;
  padding: 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  gap: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
}

/* Logo inside pill navigation */
.navbar-logo-pill {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.navbar-logo-pill:hover {
  transform: scale(1.05);
}

.logo-icon-pill {
  width: 32px;
  height: 32px;
  background: linear-gradient(to bottom right, var(--light-blue), var(--primary-purple));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 14px;
  animation: azure-pulse 3s ease-in-out infinite;
}

.logo-text-pill {
  font-size: 16px;
  font-weight: 700;
  background: linear-gradient(to right, var(--light-blue), var(--primary-purple), var(--dark-blue));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Navigation divider */
.nav-divider {
  width: 1px;
  height: 32px;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 0 8px;
}

.navbar-menu-pill:hover {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.navbar-pill-link {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 40px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #6b7280;
  text-decoration: none;
  white-space: nowrap;
  min-height: 44px;
  justify-content: center;
}

.navbar-pill-link:not(.active) {
  color: #6b7280;
  background: transparent;
}

.navbar-pill-link:not(.active):hover {
  color: #374151;
  background: rgba(0, 0, 0, 0.05);
}

.navbar-pill-link.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.pill-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  color: white;
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
}

.cart-count-pill {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 18px;
  height: 18px;
  background: linear-gradient(to right, #ef4444, #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 11px;
  font-weight: 700;
  border: 2px solid white;
}

/* Responsive pill navigation */
@media (max-width: 1200px) {
  .navbar-menu-pill {
    gap: 2px;
    padding: 6px;
  }

  .navbar-pill-link {
    padding: 10px 16px;
    font-size: 13px;
  }

  .pill-badge {
    width: 22px;
    height: 22px;
    font-size: 11px;
  }
}

@media (max-width: 1024px) {
  .navbar {
    top: 15px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 0;
    padding: 0;
  }

  .navbar-container {
    padding: 0 var(--spacing-md);
  }

  .navbar-content {
    height: 60px;
    justify-content: space-between;
  }

  .navbar-menu-pill {
    display: none;
  }

  .mobile-header {
    display: flex;
  }

  .mobile-menu-button {
    display: block;
  }

  body {
    padding-top: 90px; /* Adjusted for mobile navbar */
  }
}

@media (max-width: 768px) {
  .navbar {
    top: 10px;
  }

  body {
    padding-top: 80px; /* Further reduced for small screens */
  }
}

/* ===== HOME PAGE STYLES ===== */
.home-page {
  width: 100%;
  overflow-x: hidden;
}

.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom right, var(--light-blue), var(--primary-purple), var(--dark-blue));
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.hero-gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: repeat;
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 0 var(--spacing-md);
  max-width: 80rem;
  margin: 0 auto;
  transition: all 1s;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 4.5rem);
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--spacing-xl);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-md);
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-price-badge {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: inline-block;
}

.hero-price-text {
  font-size: clamp(1.5rem, 5vw, 2rem);
  font-weight: 700;
  color: #fbbf24;
  animation: bounce 1s infinite;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  align-items: center;
}

.hero-button {
  background: linear-gradient(to right, var(--light-blue), var(--azure-blue));
  color: var(--white);
  font-weight: 600;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-xl);
  font-size: 1.125rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-large);
  animation: azure-pulse 3s ease-in-out infinite;
}

.hero-button:hover {
  background: linear-gradient(to right, var(--azure-blue), var(--dark-blue));
  transform: translateY(-2px);
  box-shadow: var(--shadow-azure);
}

/* ===== SHOP PAGE STYLES ===== */
.shop {
  min-height: 100vh;
  background: var(--background-light);
  padding: var(--spacing-xl) 0;
  flex: 1;
}

.shop-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.shop-header h1 {
  font-size: clamp(2rem, 5vw, 3rem);
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
  animation: azure-blink 3s ease-in-out infinite;
}

.shop-header p {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  color: var(--primary-blue);
  font-weight: 600;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.product-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-large);
  transition: all var(--transition-normal);
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  animation: azure-blink 2s ease-in-out infinite;
}

.product-image {
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-add-btn {
  background: var(--white);
  color: var(--primary-blue);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 25px;
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-normal);
  transform: translateY(20px);
}

.product-card:hover .quick-add-btn {
  transform: translateY(0);
}

.quick-add-btn:hover {
  background: #f0f8ff;
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

.product-info {
  padding: var(--spacing-lg);
}

.product-info h3 {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-dark);
  font-weight: 600;
}

.product-description {
  color: var(--text-gray);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  font-size: 0.95rem;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-blue);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.add-to-cart-btn {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all var(--transition-normal);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
  animation: azure-blink 1s ease-in-out infinite;
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

/* ===== CART PAGE STYLES ===== */
.cart {
  min-height: 100vh;
  background: var(--background-light);
  padding: var(--spacing-xl) 0;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.cart-header h1 {
  font-size: 2.5rem;
  color: var(--text-dark);
  animation: azure-blink 3s ease-in-out infinite;
}

.clear-cart-btn {
  background: var(--red);
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-normal);
}

.clear-cart-btn:hover {
  background: #c82333;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.empty-cart {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-cart-content {
  text-align: center;
  padding: 4rem var(--spacing-xl);
}

.empty-cart-icon {
  font-size: 5rem;
  margin-bottom: var(--spacing-md);
  animation: azure-blink 2s ease-in-out infinite;
}

.empty-cart-content h2 {
  font-size: 2rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

.empty-cart-content p {
  font-size: 1.1rem;
  color: var(--text-gray);
  margin-bottom: var(--spacing-xl);
}

.continue-shopping-btn {
  display: inline-block;
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-md) var(--spacing-xl);
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  transition: all var(--transition-normal);
}

.continue-shopping-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
  animation: azure-blink 1s ease-in-out infinite;
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
}

.cart-items {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-large);
}

.cart-item {
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) 0;
  border-bottom: 1px solid #eee;
  transition: all var(--transition-normal);
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item:hover {
  background: var(--background-light);
  border-radius: var(--radius-md);
  animation: azure-blink 2s ease-in-out infinite;
}

.cart-item img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.item-details h3 {
  font-size: 1.2rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
}

.item-details p {
  color: var(--text-gray);
  font-size: 0.9rem;
  margin-bottom: var(--spacing-md);
}

.item-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--background-light);
  border-radius: 25px;
  padding: var(--spacing-xs);
}

.quantity-btn {
  background: var(--primary-blue);
  color: var(--white);
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-weight: 700;
  transition: all var(--transition-normal);
}

.quantity-btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.remove-btn {
  background: var(--red);
  color: var(--white);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all var(--transition-normal);
}

.remove-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.item-price {
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.unit-price {
  font-size: 0.9rem;
  color: var(--text-gray);
  margin-bottom: var(--spacing-sm);
}

.total-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-blue);
}

.cart-summary {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-large);
  height: fit-content;
  position: sticky;
  top: var(--spacing-xl);
}

.cart-summary h3 {
  font-size: 1.5rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.summary-row.total {
  border-top: 2px solid #eee;
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--text-dark);
}

.checkout-btn {
  display: block;
  width: 100%;
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-md);
  text-decoration: none;
  border-radius: 25px;
  text-align: center;
  font-weight: 600;
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  transition: all var(--transition-normal);
  animation: azure-blink 3s ease-in-out infinite;
}

.checkout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
  animation: azure-blink 1s ease-in-out infinite;
}

.continue-shopping {
  display: block;
  text-align: center;
  color: var(--primary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-normal);
}

.continue-shopping:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

/* ===== CHECKOUT PAGE STYLES ===== */
.checkout {
  min-height: 100vh;
  background: var(--background-light);
  padding: var(--spacing-xl) 0;
}

.checkout h1 {
  font-size: 2.5rem;
  color: var(--text-dark);
  text-align: center;
  margin-bottom: var(--spacing-xl);
  animation: azure-blink 3s ease-in-out infinite;
}

.empty-checkout {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  padding: 4rem var(--spacing-xl);
}

.empty-content h2 {
  font-size: 2rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

.empty-content p {
  font-size: 1.1rem;
  color: var(--text-gray);
  margin-bottom: var(--spacing-xl);
}

.shop-btn {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all var(--transition-normal);
}

.shop-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
  animation: azure-blink 1s ease-in-out infinite;
}

.checkout-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
}

.checkout-form {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-large);
}

.form-section {
  margin-bottom: var(--spacing-xl);
}

.form-section h3 {
  font-size: 1.3rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-blue);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.checkout-form input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid #e9ecef;
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-md);
}

.checkout-form input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  animation: azure-blink 1s ease-in-out infinite;
}

.place-order-btn {
  width: 100%;
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  padding: 1.2rem;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  animation: azure-blink 3s ease-in-out infinite;
}

.place-order-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
  animation: azure-blink 1s ease-in-out infinite;
}

.place-order-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.order-summary {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-large);
  height: fit-content;
  position: sticky;
  top: var(--spacing-xl);
}

.order-summary h3 {
  font-size: 1.5rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.order-items {
  margin-bottom: var(--spacing-lg);
}

.order-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--radius-sm);
}

.item-info {
  flex: 1;
}

.item-info h4 {
  font-size: 1rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
}

.item-info p {
  font-size: 0.9rem;
  color: var(--text-gray);
}

.item-total {
  font-weight: 600;
  color: var(--primary-blue);
}

.summary-totals {
  border-top: 2px solid #eee;
  padding-top: var(--spacing-md);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
  .container {
    padding: 0 var(--spacing-2xl);
  }

  .cart {
    padding: var(--spacing-2xl) 0;
  }

  .cart-content {
    gap: var(--spacing-2xl);
  }

  .cart-item {
    grid-template-columns: 140px 1fr auto;
    gap: var(--spacing-lg);
  }

  .cart-item img {
    width: 140px;
    height: 140px;
  }

  .checkout {
    padding: var(--spacing-2xl) 0;
  }

  .checkout-content {
    gap: var(--spacing-2xl);
  }

  .checkout-form {
    padding: 2.5rem;
  }

  .order-summary {
    padding: var(--spacing-xl);
  }

  .shop {
    padding: var(--spacing-2xl) 0;
  }

  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  .product-image img {
    height: 280px;
  }
}

/* Tablet (768px to 1024px) */
@media (max-width: 1024px) {
  .navbar-menu {
    display: none;
  }

  .navbar-menu-pill {
    display: none;
  }

  .mobile-menu-button {
    display: block;
  }

  .cart {
    padding: var(--spacing-lg) 0;
  }

  .cart-content {
    gap: var(--spacing-lg);
  }

  .cart-summary {
    top: var(--spacing-lg);
  }

  .checkout {
    padding: var(--spacing-lg) 0;
  }

  .checkout-content {
    gap: var(--spacing-lg);
  }

  .order-summary {
    top: var(--spacing-lg);
  }

  .shop {
    padding: var(--spacing-lg) 0;
  }

  .shop-header {
    margin-bottom: 2.5rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
  }
}

/* Mobile (768px and below) */
@media (max-width: 768px) {
  .hero-buttons {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .hero-buttons > div {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .cart {
    padding: var(--spacing-md) 0;
  }

  .cart-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .cart-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .cart-header h1 {
    font-size: 2rem;
  }

  .cart-item {
    grid-template-columns: 100px 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
  }

  .cart-item img {
    width: 100px;
    height: 100px;
  }

  .item-details h3 {
    font-size: 1.1rem;
  }

  .item-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .quantity-controls {
    align-self: flex-start;
  }

  .item-price {
    grid-column: 1 / -1;
    text-align: left;
    margin-top: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cart-summary {
    position: static;
    margin-top: 0;
  }

  .checkout {
    padding: var(--spacing-md) 0;
  }

  .checkout h1 {
    font-size: 2rem;
    margin-bottom: var(--spacing-lg);
  }

  .checkout-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .checkout-form {
    padding: var(--spacing-lg);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .form-section h3 {
    font-size: 1.2rem;
  }

  .checkout-form input {
    padding: var(--spacing-md);
    font-size: 1rem;
  }

  .place-order-btn {
    padding: 1.2rem;
    font-size: 1rem;
  }

  .order-summary {
    position: static;
    margin-top: 0;
  }

  .order-item {
    padding: var(--spacing-md) 0;
  }

  .order-item img {
    width: 50px;
    height: 50px;
  }

  .item-info h4 {
    font-size: 0.9rem;
  }

  .item-info p {
    font-size: 0.8rem;
  }

  .shop {
    padding: var(--spacing-lg) 0;
  }

  .shop-header {
    margin-bottom: var(--spacing-xl);
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .product-image img {
    height: 200px;
  }

  .product-info {
    padding: 1.2rem;
  }

  .add-to-cart-btn {
    padding: var(--spacing-md) 1.2rem;
    font-size: 0.9rem;
  }

  .quick-add-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.9rem;
  }
}

/* Small Mobile (480px and below) */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .hero-subtitle {
    font-size: clamp(1rem, 4vw, 1.25rem);
  }

  .hero-price-text {
    font-size: clamp(1.25rem, 5vw, 1.5rem);
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
    gap: var(--spacing-md);
  }

  .cart-item img {
    width: 80px;
    height: 80px;
  }

  .item-details {
    min-width: 0;
  }

  .item-details h3 {
    font-size: 1rem;
    line-height: 1.3;
  }

  .item-details p {
    font-size: 0.85rem;
  }

  .quantity-btn {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .remove-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .cart-summary {
    padding: 1.2rem;
  }

  .checkout-btn {
    padding: 1.2rem;
    font-size: 1rem;
  }

  .checkout-form {
    padding: var(--spacing-md);
  }

  .form-section {
    margin-bottom: var(--spacing-lg);
  }

  .form-section h3 {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
  }

  .checkout-form input {
    padding: 0.9rem;
    margin-bottom: var(--spacing-md);
    font-size: 0.95rem;
  }

  .place-order-btn {
    padding: 1.1rem;
    font-size: 0.95rem;
  }

  .order-summary {
    padding: 1.2rem;
  }

  .order-summary h3 {
    font-size: 1.3rem;
  }

  .empty-content {
    padding: var(--spacing-2xl) var(--spacing-md);
  }

  .empty-content h2 {
    font-size: 1.8rem;
  }

  .empty-content p {
    font-size: 1rem;
  }

  .shop-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
  }

  .product-image img {
    height: 180px;
  }

  .product-info {
    padding: var(--spacing-md);
  }

  .product-footer {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .add-to-cart-btn {
    width: 100%;
    padding: var(--spacing-md);
    font-size: 1rem;
  }

  .quick-add-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
  }
}

/* ===== ADDITIONAL UTILITY CLASSES ===== */

/* CSS Variables for different themes (for future customization) */
:root[data-theme="dark"] {
  --text-dark: #f8fafc;
  --text-gray: #cbd5e1;
  --background-light: #1e293b;
  --background-gray: #334155;
  --white: #0f172a;
}

/* Print styles */
@media print {
  .navbar,
  .mobile-menu,
  .hero-section,
  .cart-summary,
  .order-summary {
    display: none;
  }

  .cart-item,
  .product-card {
    break-inside: avoid;
  }

  body {
    background: white;
    color: black;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.3);
    --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.3);
  }

  .btn-primary,
  .btn-azure,
  .add-to-cart-btn,
  .checkout-btn {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-azure-glow,
  .animate-bounce,
  .animate-fade-in-up,
  .animate-fade-in-down,
  .animate-slide-in-right {
    animation: none;
  }
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--light-blue);
  outline-offset: 2px;
}

button:focus,
a:focus,
input:focus {
  outline: 2px solid var(--light-blue);
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* End of PayShop CSS */
