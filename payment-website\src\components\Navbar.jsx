import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useCart } from '../context/CartContext';

const Navbar = () => {
  const { getCartItemsCount } = useCart();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const totalItems = getCartItemsCount();

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <div className="navbar-content">
          {/* Desktop Navigation with Logo */}
          <div className="navbar-menu-pill">
            {/* Logo inside pill */}
            <Link to="/" className="navbar-logo-pill">
              <div className="logo-icon-pill">
                <span>P</span>
              </div>
              <span className="logo-text-pill">PayShop</span>
            </Link>

            {/* Navigation divider */}
            <div className="nav-divider"></div>

            {/* Navigation Links */}
            <Link
              to="/"
              className={`navbar-pill-link ${isActive('/') ? 'active' : ''}`}
            >
              <span>Home</span>
              {isActive('/') && (
                <div className="pill-badge">
                  <span>4</span>
                </div>
              )}
            </Link>

            <Link
              to="/shop"
              className={`navbar-pill-link ${isActive('/shop') ? 'active' : ''}`}
            >
              <span>Products</span>
            </Link>

            <Link
              to="/cart"
              className={`navbar-pill-link ${isActive('/cart') ? 'active' : ''}`}
            >
              <span>Cart</span>
              {totalItems > 0 && !isActive('/cart') && (
                <div className="cart-count-pill">
                  <span>{totalItems}</span>
                </div>
              )}
              {isActive('/cart') && (
                <div className="pill-badge">
                  <span>{totalItems || '0'}</span>
                </div>
              )}
            </Link>
          </div>

          {/* Mobile Logo and Menu Button */}
          <div className="mobile-header">
            <Link to="/" className="navbar-logo-mobile">
              <div className="logo-icon">
                <span>P</span>
              </div>
              <span className="logo-text">PayShop</span>
            </Link>

            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="mobile-menu-button"
            >
              <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="mobile-menu">
            <div className="mobile-menu-links">
              <Link
                to="/"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`mobile-menu-link ${isActive('/') ? 'active' : ''}`}
              >
                Home
              </Link>

              <Link
                to="/shop"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`mobile-menu-link ${isActive('/shop') ? 'active' : ''}`}
              >
                Shop
              </Link>

              <Link
                to="/cart"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`mobile-menu-link ${isActive('/cart') ? 'active' : ''}`}
              >
                <span>Cart</span>
                {totalItems > 0 && (
                  <div className="cart-count">
                    <span>{totalItems}</span>
                  </div>
                )}
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
